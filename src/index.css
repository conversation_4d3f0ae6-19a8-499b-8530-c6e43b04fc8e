@keyframes hero-bg-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-animated-hero {
  background: linear-gradient(120deg, hsl(var(--brand-dark-navy)) 0%, hsl(var(--brand-primary-blue)) 50%, hsl(var(--brand-teal)) 100%);
  background-size: 200% 200%;
  animation: hero-bg-move 8s ease-in-out infinite;
}
/* tailwindcss */
/* Remove the following lines if not using Tailwind's build process:
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

/* TriAgentic AI Design System - Official Brand Colors
All colors converted to HSL format for CSS custom properties.
Brand Guidelines: Less Admin. More Care.
*/

@layer base {
  :root {
    /* TriAgentic AI Official Brand Colors */
    --brand-primary-blue: 225 58% 41%;        /* #2E50A7 - Main brand color */
    --brand-dark-navy: 225 65% 11%;           /* #0A142F - Dark backgrounds, nav, footer */
    --brand-teal: 191 100% 50%;               /* #00CFFF - Triangle, highlights, CTAs */
    --brand-deep-purple: 250 100% 69%;        /* #7B61FF - Alternative accent */
    --brand-light-grey-blue: 214 44% 96%;     /* #F4F7FA - Light backgrounds */

    /* Status Colors */
    --brand-error-red: 3 65% 57%;             /* #D9534F - Error states */
    --brand-success-green: 134 61% 41%;       /* #28A745 - Success states */

    /* Text Colors */
    --brand-text-primary: 0 0% 100%;          /* #FFFFFF - Text on dark backgrounds */
    --brand-text-secondary: 0 0% 20%;         /* #333333 - Body copy on light backgrounds */
    --brand-text-muted: 0 0% 33%;             /* #555555 - Subtext, labels, placeholders */

    /* Core System Colors - Mapped to TriAgentic Brand */
    --background: 214 44% 96%;                 /* Light grey-blue background */
    --foreground: 0 0% 20%;                    /* Secondary text color */

    --card: 0 0% 100%;                         /* White cards */
    --card-foreground: 0 0% 20%;               /* Secondary text on cards */

    --popover: 0 0% 100%;                      /* White popovers */
    --popover-foreground: 0 0% 20%;            /* Secondary text on popovers */

    --primary: 225 58% 41%;                    /* Primary blue */
    --primary-foreground: 0 0% 100%;           /* White text on primary */

    --secondary: 214 44% 96%;                  /* Light grey-blue */
    --secondary-foreground: 0 0% 20%;          /* Secondary text */

    --muted: 214 44% 96%;                      /* Light grey-blue */
    --muted-foreground: 0 0% 33%;              /* Muted text */

    --accent: 191 100% 50%;                    /* Teal accent */
    --accent-foreground: 225 65% 11%;          /* Dark navy text on teal */

    --destructive: 3 65% 57%;                  /* Error red */
    --destructive-foreground: 0 0% 100%;       /* White text on error */

    --border: 214 32% 91%;                     /* Light border */
    --input: 214 44% 96%;                      /* Light input background */
    --ring: 191 100% 50%;                      /* Teal focus ring */

    /* TriAgentic AI Brand Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-primary-blue)), hsl(var(--brand-teal)));
    --gradient-feature: linear-gradient(135deg, hsl(var(--brand-teal)), hsl(var(--brand-deep-purple)));
    --gradient-cta: linear-gradient(135deg, hsl(var(--brand-primary-blue)), hsl(var(--brand-deep-purple)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--secondary)));

    /* Professional Medical Shadows */
    --shadow-professional: 0 4px 20px hsl(var(--brand-primary-blue) / 0.15);
    --shadow-card: 0 2px 10px hsl(var(--brand-teal) / 0.1);
    --shadow-feature: 0 8px 30px hsl(var(--brand-deep-purple) / 0.2);

    /* Typography Scale for Medical Content */
    --font-medical-hero: 3.5rem;
    --font-medical-heading: 2.5rem;
    --font-medical-subheading: 1.5rem;
    --font-medical-body: 1.125rem;

    --radius: 0.5rem;

    /* Sidebar Colors - Using TriAgentic Brand */
    --sidebar-background: 214 44% 96%;         /* Light grey-blue */
    --sidebar-foreground: 0 0% 20%;            /* Secondary text */
    --sidebar-primary: 225 65% 11%;            /* Dark navy */
    --sidebar-primary-foreground: 0 0% 100%;   /* White text */
    --sidebar-accent: 191 100% 50%;            /* Teal accent */
    --sidebar-accent-foreground: 225 65% 11%;  /* Dark navy text */
    --sidebar-border: 214 32% 91%;             /* Light border */
    --sidebar-ring: 191 100% 50%;              /* Teal ring */
  }

  .dark {
    /* Dark Mode - Using TriAgentic Dark Navy as Primary Background */
    --background: 225 65% 11%;                 /* Dark navy background */
    --foreground: 0 0% 100%;                   /* White text */

    --card: 225 65% 15%;                       /* Slightly lighter navy for cards */
    --card-foreground: 0 0% 100%;              /* White text on cards */

    --popover: 225 65% 15%;                    /* Slightly lighter navy for popovers */
    --popover-foreground: 0 0% 100%;           /* White text on popovers */

    --primary: 191 100% 50%;                   /* Teal primary in dark mode */
    --primary-foreground: 225 65% 11%;         /* Dark navy text on teal */

    --secondary: 225 65% 18%;                  /* Lighter navy for secondary */
    --secondary-foreground: 0 0% 100%;         /* White text */

    --muted: 225 65% 18%;                      /* Lighter navy for muted */
    --muted-foreground: 0 0% 70%;              /* Light grey text */

    --accent: 191 100% 50%;                    /* Teal accent */
    --accent-foreground: 225 65% 11%;          /* Dark navy text on teal */

    --destructive: 3 65% 57%;                  /* Error red (same as light mode) */
    --destructive-foreground: 0 0% 100%;       /* White text on error */

    --border: 225 65% 20%;                     /* Lighter navy border */
    --input: 225 65% 18%;                      /* Lighter navy input background */
    --ring: 191 100% 50%;                      /* Teal focus ring */

    /* Dark Mode Sidebar */
    --sidebar-background: 225 65% 11%;         /* Dark navy */
    --sidebar-foreground: 0 0% 100%;           /* White text */
    --sidebar-primary: 191 100% 50%;           /* Teal primary */
    --sidebar-primary-foreground: 225 65% 11%; /* Dark navy text */
    --sidebar-accent: 225 65% 18%;             /* Lighter navy accent */
    --sidebar-accent-foreground: 0 0% 100%;    /* White text */
    --sidebar-border: 225 65% 20%;             /* Lighter navy border */
    --sidebar-ring: 191 100% 50%;              /* Teal ring */
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
