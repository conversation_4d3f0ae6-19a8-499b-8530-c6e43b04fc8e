import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const Contact = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Demo Request Submitted!",
        description: "Our team will contact you within 24 hours to schedule your demo.",
      });
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-hero">
          <div className="container mx-auto px-4 text-center text-white">
            <h1 className="text-4xl md:text-medical-heading font-bold mb-4">
              Book Your Free Demo
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              See how Triagentic AI can transform your medical practice. 
              Schedule a personalized demo with our healthcare technology specialists.
            </p>
          </div>
        </section>

        {/* Contact Form Section */}
        <section className="py-20 bg-gradient-subtle">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <Card className="shadow-professional">
                  <CardHeader>
                    <CardTitle className="text-2xl">Request Your Demo</CardTitle>
                    <CardDescription>
                      Fill out the form below and our team will reach out to schedule your personalized demonstration.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">First Name *</Label>
                          <Input id="firstName" required />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName">Last Name *</Label>
                          <Input id="lastName" required />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <Input id="email" type="email" required />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input id="phone" type="tel" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="practice">Practice/Organization Name *</Label>
                        <Input id="practice" required />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="role">Your Role *</Label>
                        <Select required>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="physician">Physician</SelectItem>
                            <SelectItem value="administrator">Practice Administrator</SelectItem>
                            <SelectItem value="it-manager">IT Manager</SelectItem>
                            <SelectItem value="practice-manager">Practice Manager</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="practiceSize">Practice Size</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select practice size" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1-5">1-5 providers</SelectItem>
                            <SelectItem value="6-20">6-20 providers</SelectItem>
                            <SelectItem value="21-50">21-50 providers</SelectItem>
                            <SelectItem value="50+">50+ providers</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message">How can Triagentic AI help your practice?</Label>
                        <Textarea 
                          id="message" 
                          placeholder="Tell us about your current challenges and goals..."
                          rows={4}
                        />
                      </div>

                      <Button 
                        type="submit" 
                        variant="demo" 
                        size="lg" 
                        className="w-full"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? "Submitting..." : "Schedule Demo"}
                      </Button>
                    </form>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <div className="space-y-8">
                  <Card className="shadow-card">
                    <CardHeader>
                      <CardTitle>Why Choose Triagentic AI?</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-hero rounded-full flex items-center justify-center mt-0.5">
                          <span className="text-white text-sm">✓</span>
                        </div>
                        <div>
                          <div className="font-medium">Proven Results</div>
                          <div className="text-sm text-muted-foreground">500+ practices saving 5-10 hours per week</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-hero rounded-full flex items-center justify-center mt-0.5">
                          <span className="text-white text-sm">✓</span>
                        </div>
                        <div>
                          <div className="font-medium">Quick Implementation</div>
                          <div className="text-sm text-muted-foreground">Up and running in under 48 hours</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-hero rounded-full flex items-center justify-center mt-0.5">
                          <span className="text-white text-sm">✓</span>
                        </div>
                        <div>
                          <div className="font-medium">Dedicated Support</div>
                          <div className="text-sm text-muted-foreground">24/7 technical support and training</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="shadow-card">
                    <CardHeader>
                      <CardTitle>Get in Touch</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <div className="font-medium">Sales & Demos</div>
                        <div className="text-muted-foreground">1-800-TRIAGEN</div>
                      </div>
                      <div>
                        <div className="font-medium">Email</div>
                        <div className="text-muted-foreground"><EMAIL></div>
                      </div>
                      <div>
                        <div className="font-medium">Support</div>
                        <div className="text-muted-foreground">Available 24/7 for existing customers</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Contact;