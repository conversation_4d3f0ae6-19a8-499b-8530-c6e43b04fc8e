import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const BrandShowcase = () => {
  return (
    <section className="py-20 bg-brand-light-grey-blue">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-brand-text-secondary mb-4">
              TriAgentic AI Brand Colors
            </h2>
            <p className="text-lg text-brand-text-muted max-w-2xl mx-auto">
              Less Admin. More Care. - Our brand colors reflect professionalism, trust, and innovation in healthcare AI.
            </p>
          </div>

          {/* Primary Brand Colors */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-brand-text-secondary mb-8">Primary Brand Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Primary Blue */}
              <Card className="overflow-hidden">
                <div className="h-32 bg-brand-primary-blue"></div>
                <CardHeader>
                  <CardTitle className="text-brand-text-secondary">Primary Blue</CardTitle>
                  <CardDescription>#2E50A7</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-brand-text-muted">
                    Main brand color for buttons, links, headlines, and branding blocks.
                  </p>
                </CardContent>
              </Card>

              {/* Dark Navy */}
              <Card className="overflow-hidden">
                <div className="h-32 bg-brand-dark-navy"></div>
                <CardHeader>
                  <CardTitle className="text-brand-text-secondary">Dark Navy</CardTitle>
                  <CardDescription>#0A142F</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-brand-text-muted">
                    Main dark background, navigation bars, footers.
                  </p>
                </CardContent>
              </Card>

              {/* Teal */}
              <Card className="overflow-hidden">
                <div className="h-32 bg-brand-teal"></div>
                <CardHeader>
                  <CardTitle className="text-brand-text-secondary">Teal</CardTitle>
                  <CardDescription>#00CFFF</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-brand-text-muted">
                    Logo triangle, CTA highlights, icon accents.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Accent & Status Colors */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-brand-text-secondary mb-8">Accent & Status Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Deep Purple */}
              <Card className="overflow-hidden">
                <div className="h-24 bg-brand-deep-purple"></div>
                <CardHeader className="pb-4">
                  <CardTitle className="text-brand-text-secondary text-lg">Deep Purple</CardTitle>
                  <CardDescription>#7B61FF</CardDescription>
                </CardHeader>
              </Card>

              {/* Success Green */}
              <Card className="overflow-hidden">
                <div className="h-24 bg-brand-success-green"></div>
                <CardHeader className="pb-4">
                  <CardTitle className="text-brand-text-secondary text-lg">Success Green</CardTitle>
                  <CardDescription>#28A745</CardDescription>
                </CardHeader>
              </Card>

              {/* Error Red */}
              <Card className="overflow-hidden">
                <div className="h-24 bg-brand-error-red"></div>
                <CardHeader className="pb-4">
                  <CardTitle className="text-brand-text-secondary text-lg">Error Red</CardTitle>
                  <CardDescription>#D9534F</CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>

          {/* Interactive Elements Demo */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-brand-text-secondary mb-8">Interactive Elements</h3>
            <Card className="p-8">
              <div className="space-y-6">
                {/* Buttons */}
                <div>
                  <h4 className="text-lg font-medium text-brand-text-secondary mb-4">Buttons</h4>
                  <div className="flex flex-wrap gap-4">
                    <Button className="bg-brand-primary-blue hover:bg-brand-primary-blue/90 text-brand-text-primary">
                      Primary Button
                    </Button>
                    <Button className="bg-brand-teal hover:bg-brand-teal/90 text-brand-dark-navy">
                      Teal CTA
                    </Button>
                    <Button variant="outline" className="border-brand-primary-blue text-brand-primary-blue hover:bg-brand-primary-blue hover:text-brand-text-primary">
                      Outline Button
                    </Button>
                    <Button variant="destructive" className="bg-brand-error-red hover:bg-brand-error-red/90">
                      Error Action
                    </Button>
                  </div>
                </div>

                {/* Badges */}
                <div>
                  <h4 className="text-lg font-medium text-brand-text-secondary mb-4">Badges</h4>
                  <div className="flex flex-wrap gap-3">
                    <Badge className="bg-brand-teal text-brand-dark-navy">Featured</Badge>
                    <Badge className="bg-brand-success-green text-brand-text-primary">Active</Badge>
                    <Badge variant="outline" className="border-brand-primary-blue text-brand-primary-blue">
                      Professional
                    </Badge>
                    <Badge className="bg-brand-deep-purple text-brand-text-primary">Premium</Badge>
                  </div>
                </div>

                {/* Typography */}
                <div>
                  <h4 className="text-lg font-medium text-brand-text-secondary mb-4">Typography</h4>
                  <div className="space-y-2">
                    <h1 className="text-3xl font-bold text-brand-text-secondary">
                      Primary Heading Text
                    </h1>
                    <p className="text-lg text-brand-text-secondary">
                      Secondary body text for main content and descriptions.
                    </p>
                    <p className="text-sm text-brand-text-muted">
                      Muted text for labels, placeholders, and supporting information.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Dark Mode Preview */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-brand-text-secondary mb-8">Dark Mode Preview</h3>
            <Card className="bg-brand-dark-navy text-brand-text-primary p-8">
              <div className="space-y-6">
                <h4 className="text-xl font-semibold">TriAgentic AI Dashboard</h4>
                <p className="text-white/90">
                  Experience our platform in dark mode with the signature dark navy background 
                  and teal accent highlights.
                </p>
                <div className="flex gap-4">
                  <Button className="bg-brand-teal text-brand-dark-navy hover:bg-brand-teal/90">
                    Primary Action
                  </Button>
                  <Button variant="outline" className="border-brand-teal text-brand-teal hover:bg-brand-teal hover:text-brand-dark-navy">
                    Secondary Action
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-2xl font-bold text-brand-teal mb-1">5-10hrs</div>
                    <div className="text-white/80 text-sm">Saved per week</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-2xl font-bold text-brand-teal mb-1">35%</div>
                    <div className="text-white/80 text-sm">Revenue increase</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-2xl font-bold text-brand-teal mb-1">95%</div>
                    <div className="text-white/80 text-sm">Accuracy rate</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Brand Guidelines Summary */}
          <div className="text-center">
            <Card className="p-8 bg-gradient-hero text-brand-text-primary">
              <h3 className="text-2xl font-bold mb-4">TriAgentic AI Brand Guidelines</h3>
              <p className="text-lg mb-6 text-white/90">
                "Less Admin. More Care." - Empowering healthcare professionals with AI that reduces 
                administrative burden and enhances patient care.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Badge className="bg-white/20 text-white border-white/30">Professional</Badge>
                <Badge className="bg-white/20 text-white border-white/30">Empowering</Badge>
                <Badge className="bg-white/20 text-white border-white/30">Clear</Badge>
                <Badge className="bg-white/20 text-white border-white/30">Practical</Badge>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BrandShowcase;
