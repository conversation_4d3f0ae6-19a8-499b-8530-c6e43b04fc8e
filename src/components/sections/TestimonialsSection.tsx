import { Card, CardContent } from "@/components/ui/card";

const TestimonialsSection = () => {
  const testimonials = [
    {
      quote: "Triagentic AI has transformed our practice efficiency. We're seeing 40% more patients with the same staff, and our revenue has increased by 35% in just 6 months.",
      author: "Dr. <PERSON>",
      role: "Chief Medical Officer",
      practice: "Metro Family Medicine",
      avatar: "SC"
    },
    {
      quote: "The AI workflow automation eliminated hours of administrative work daily. Our team can now focus on what matters most - patient care.",
      author: "Dr. <PERSON>",
      role: "Practice Director",
      practice: "Riverside Medical Group",
      avatar: "MR"
    },
    {
      quote: "Implementation was seamless, and the ROI was evident within weeks. This technology is essential for modern medical practices.",
      author: "Dr. <PERSON>",
      role: "Managing Partner",
      practice: "Thompson Healthcare Associates",
      avatar: "ET"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-medical-heading font-bold text-foreground mb-4">
            Trusted by Healthcare Professionals
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            See how medical practices are transforming their operations with Triagentic AI.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={index} 
              className="relative overflow-hidden shadow-card hover:shadow-professional transition-all duration-300 border-0 bg-white"
            >
              {/* Quote Icon */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-hero rounded-full flex items-center justify-center opacity-20">
                <span className="text-white text-lg font-bold">"</span>
              </div>
              
              <CardContent className="p-6">
                {/* Quote */}
                <blockquote className="text-muted-foreground leading-relaxed mb-6 italic">
                  "{testimonial.quote}"
                </blockquote>
                
                {/* Author Info */}
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-hero rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">{testimonial.avatar}</span>
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.author}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                    <div className="text-sm text-brand-light-blue font-medium">{testimonial.practice}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="bg-gradient-subtle rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Recognized Excellence in Healthcare Technology
            </h3>
            <p className="text-muted-foreground">
              Industry awards and certifications that demonstrate our commitment to quality
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-white rounded-lg shadow-card">
              <div className="w-12 h-12 bg-gradient-hero rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">★</span>
              </div>
              <div className="text-sm font-medium text-foreground">HIPAA Compliant</div>
              <div className="text-xs text-muted-foreground mt-1">Security Certified</div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow-card">
              <div className="w-12 h-12 bg-gradient-feature rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">✓</span>
              </div>
              <div className="text-sm font-medium text-foreground">FDA Approved</div>
              <div className="text-xs text-muted-foreground mt-1">Medical AI Platform</div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow-card">
              <div className="w-12 h-12 bg-gradient-cta rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">⚡</span>
              </div>
              <div className="text-sm font-medium text-foreground">SOC 2 Type II</div>
              <div className="text-xs text-muted-foreground mt-1">Security Standards</div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow-card">
              <div className="w-12 h-12 bg-gradient-hero rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">◆</span>
              </div>
              <div className="text-sm font-medium text-foreground">HL7 FHIR</div>
              <div className="text-xs text-muted-foreground mt-1">Interoperability</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;