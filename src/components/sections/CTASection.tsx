import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Link } from "react-router-dom";

const CTASection = () => {
  return (
    <section className="py-20 bg-gradient-cta relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="cta-dots" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill="white" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#cta-dots)" />
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center text-white">
          {/* Main CTA Content */}
          <div className="mb-12">
            <h2 className="text-3xl md:text-medical-heading font-bold mb-6">
              Ready to Transform Your Medical Practice?
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Join 500+ medical practices already saving time and increasing revenue with Triagentic AI. 
              See results in as little as 30 days.
            </p>
          </div>

          {/* Value Props Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold mb-2">Free Demo</div>
                <div className="text-white/80 text-sm">See Triagentic AI in action with your practice data</div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold mb-2">Quick Setup</div>
                <div className="text-white/80 text-sm">Implementation in under 48 hours</div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold mb-2">ROI Guarantee</div>
                <div className="text-white/80 text-sm">See measurable results within 30 days</div>
              </CardContent>
            </Card>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Button 
              variant="demo" 
              size="lg" 
              className="text-lg px-10 py-4 bg-white text-brand-deep-purple hover:bg-white/90 hover:shadow-feature transform hover:scale-105"
              asChild
            >
              <Link to="/contact">Book Your Free Demo</Link>
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="text-lg px-8 py-4 border-white/50 text-white hover:bg-white/10 hover:border-white"
              asChild
            >
              <Link to="/features">Explore All Features</Link>
            </Button>
          </div>

          {/* Contact Info */}
          <div className="text-center">
            <p className="text-white/80 text-sm mb-2">
              Questions? Our medical AI specialists are here to help.
            </p>
            <p className="text-white font-medium">
              Call us at{" "}
              <a href="tel:1-800-TRIAGEN" className="underline hover:no-underline">
                1-800-TRIAGEN
              </a>
              {" "}or email{" "}
              <a href="mailto:<EMAIL>" className="underline hover:no-underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;