import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";

const FeaturesPreview = () => {
  const features = [
    {
      title: "Intelligent Workflow Automation",
      description: "Automate routine tasks and streamline patient management with AI-powered workflows that learn from your practice patterns.",
      benefits: ["Reduces admin time by 70%", "Eliminates manual errors", "24/7 automated processing"],
      gradient: "from-brand-light-blue to-brand-purple"
    },
    {
      title: "Revenue Optimization Engine",
      description: "Maximize practice revenue through intelligent billing optimization, claim management, and financial analytics.",
      benefits: ["Increases revenue by 35%", "Faster claim processing", "Reduced billing errors"],
      gradient: "from-brand-purple to-brand-deep-purple"
    },
    {
      title: "Smart Patient Engagement",
      description: "Enhance patient experience with AI-driven communication, appointment scheduling, and follow-up management.",
      benefits: ["Improves patient satisfaction", "Reduces no-shows by 40%", "Automated follow-ups"],
      gradient: "from-brand-deep-purple to-brand-magenta"
    }
  ];

  return (
    <section className="py-20 bg-gradient-subtle">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-medical-heading font-bold text-foreground mb-4">
            AI Solutions That Transform Your Practice
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Discover how Triagentic AI empowers medical professionals with cutting-edge technology 
            designed specifically for healthcare workflows.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="relative overflow-hidden shadow-card hover:shadow-feature transition-all duration-300 transform hover:scale-105 border-0"
            >
              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-5`}></div>
              
              <CardHeader className="relative">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4`}>
                  <span className="text-white font-bold text-xl">{index + 1}</span>
                </div>
                <CardTitle className="text-xl font-bold text-foreground">
                  {feature.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="relative">
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, idx) => (
                    <li key={idx} className="flex items-center text-sm text-muted-foreground">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary mr-3"></div>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Stats */}
        <div className="bg-white rounded-2xl shadow-professional p-8 mb-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-3xl font-bold text-brand-light-blue">500+</div>
              <div className="text-sm text-muted-foreground">Active Practices</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-brand-purple">2M+</div>
              <div className="text-sm text-muted-foreground">Patients Served</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-brand-deep-purple">$50M+</div>
              <div className="text-sm text-muted-foreground">Revenue Generated</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-brand-magenta">98%</div>
              <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-foreground mb-4">
            Ready to Transform Your Practice?
          </h3>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join hundreds of medical practices already saving time and increasing revenue with Triagentic AI.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="demo" size="lg" className="px-8" asChild>
              <Link to="/contact">Schedule Demo</Link>
            </Button>
            <Button variant="professional" size="lg" className="px-8" asChild>
              <Link to="/features">View All Features</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesPreview;