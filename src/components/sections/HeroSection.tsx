import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-animated-hero overflow-hidden">
      {/* ...removed background pattern... */}

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="max-w-4xl mx-auto text-center text-brand-text-primary">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-brand-teal/20 backdrop-blur-sm border border-brand-teal/30 mb-8 animate-fade-in">
            <span className="text-sm font-medium text-brand-teal">Trusted by 500+ Medical Practices</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-medical-hero font-bold leading-tight mb-6 animate-fade-in">
            Empowering Medical Practices with{" "}
            <span className="bg-gradient-to-r from-brand-teal to-brand-deep-purple bg-clip-text text-transparent">
              AI Workflows
            </span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-3xl mx-auto animate-fade-in">
            Transform your practice with intelligent automation that saves time, 
            reduces administrative burden, and increases revenue by up to 35%.
          </p>

          {/* Value Propositions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">5-10hrs</div>
              <div className="text-white/80">Saved per week</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">35%</div>
              <div className="text-white/80">Revenue increase</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">95%</div>
              <div className="text-white/80">Accuracy rate</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in">
            <Button
              size="lg"
              className="text-lg px-8 py-4 bg-brand-teal text-brand-dark-navy hover:bg-brand-teal/90 hover:shadow-feature font-semibold"
              asChild
            >
              <Link to="/contact">Book Free Demo</Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-4 border-brand-teal/50 text-brand-teal hover:bg-brand-teal/10 hover:border-brand-teal"
              asChild
            >
              <Link to="/features">Explore Features</Link>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 pt-8 border-t border-white/20">
            <p className="text-white/70 text-sm mb-4">Trusted by leading healthcare organizations</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {/* Placeholder for trust logos */}
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3 text-sm font-medium">
                Regional Medical Center
              </div>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3 text-sm font-medium">
                Healthcare Partners
              </div>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3 text-sm font-medium">
                Metro Clinic Group
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;