import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

const ContactSection = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Demo Request Submitted!",
        description: "Our team will contact you within 24 hours to schedule your demo.",
      });
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <section id="contact" className="py-20 bg-brand-light-grey-blue">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-brand-text-secondary mb-4">
              Book Your Free Demo
            </h2>
            <p className="text-xl text-brand-text-muted max-w-3xl mx-auto">
              See how TriAgentic AI can transform your medical practice. 
              Schedule a personalized demo with our healthcare technology specialists.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="shadow-professional">
              <CardHeader>
                <CardTitle className="text-2xl text-brand-text-secondary">Request Your Demo</CardTitle>
                <CardDescription>
                  Fill out the form below and our team will reach out to schedule your personalized demonstration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input id="firstName" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input id="lastName" required />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input id="email" type="email" required />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="practice">Practice/Organization Name *</Label>
                    <Input id="practice" required />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role">Your Role *</Label>
                    <Select required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="physician">Physician</SelectItem>
                        <SelectItem value="administrator">Practice Administrator</SelectItem>
                        <SelectItem value="it-manager">IT Manager</SelectItem>
                        <SelectItem value="practice-manager">Practice Manager</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="practiceSize">Practice Size</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select practice size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1-5">1-5 providers</SelectItem>
                        <SelectItem value="6-20">6-20 providers</SelectItem>
                        <SelectItem value="21-50">21-50 providers</SelectItem>
                        <SelectItem value="50+">50+ providers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">How can TriAgentic AI help your practice?</Label>
                    <Textarea 
                      id="message" 
                      placeholder="Tell us about your current challenges and goals..."
                      rows={4}
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-brand-teal text-brand-dark-navy hover:bg-brand-teal/90 font-semibold"
                    size="lg"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Schedule Demo"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle className="text-brand-text-secondary">Why Choose TriAgentic AI?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-brand-teal rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-dark-navy text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-brand-text-secondary">Proven Results</div>
                      <div className="text-sm text-brand-text-muted">500+ practices saving 5-10 hours per week</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-brand-teal rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-dark-navy text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-brand-text-secondary">Quick Implementation</div>
                      <div className="text-sm text-brand-text-muted">Up and running in under 48 hours</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-brand-teal rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-brand-dark-navy text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-brand-text-secondary">Dedicated Support</div>
                      <div className="text-sm text-brand-text-muted">24/7 technical support and training</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-card">
                <CardHeader>
                  <CardTitle className="text-brand-text-secondary">Get in Touch</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="font-medium text-brand-text-secondary">Sales & Demos</div>
                    <div className="text-brand-text-muted">1-800-TRIAGEN</div>
                  </div>
                  <div>
                    <div className="font-medium text-brand-text-secondary">Email</div>
                    <div className="text-brand-text-muted"><EMAIL></div>
                  </div>
                  <div>
                    <div className="font-medium text-brand-text-secondary">Support</div>
                    <div className="text-brand-text-muted">Available 24/7 for existing customers</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
