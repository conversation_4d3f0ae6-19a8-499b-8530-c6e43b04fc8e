import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-gradient-subtle border-t border-border">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-hero rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">T</span>
              </div>
              <span className="text-lg font-bold text-foreground">Triagentic AI</span>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Empowering medical practices with AI workflows that save time and increase revenue.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Quick Links</h3>
            <nav className="space-y-2">
              <Link to="/" className="block text-muted-foreground hover:text-foreground transition-colors text-sm">
                Home
              </Link>
              <Link to="/features" className="block text-muted-foreground hover:text-foreground transition-colors text-sm">
                Features
              </Link>
              <Link to="/about" className="block text-muted-foreground hover:text-foreground transition-colors text-sm">
                About
              </Link>
              <Link to="/contact" className="block text-muted-foreground hover:text-foreground transition-colors text-sm">
                Contact
              </Link>
            </nav>
          </div>

          {/* Solutions */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Solutions</h3>
            <nav className="space-y-2">
              <span className="block text-muted-foreground text-sm">Workflow Automation</span>
              <span className="block text-muted-foreground text-sm">Revenue Optimization</span>
              <span className="block text-muted-foreground text-sm">Time Management</span>
              <span className="block text-muted-foreground text-sm">Practice Enhancement</span>
            </nav>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Get in Touch</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>Ready to transform your practice?</p>
              <Link 
                to="/contact" 
                className="inline-block text-primary hover:text-accent font-medium transition-colors"
              >
                Book a Demo →
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-muted-foreground text-sm">
              © 2024 Triagentic AI. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-foreground transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-foreground transition-colors">Security</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;