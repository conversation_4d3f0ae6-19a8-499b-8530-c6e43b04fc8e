import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// TriAgentic AI Official Brand Colors
				brand: {
					'primary-blue': 'hsl(var(--brand-primary-blue))',     // #2E50A7
					'dark-navy': 'hsl(var(--brand-dark-navy))',           // #0A142F
					'teal': 'hsl(var(--brand-teal))',                     // #00CFFF
					'deep-purple': 'hsl(var(--brand-deep-purple))',       // #7B61FF
					'light-grey-blue': 'hsl(var(--brand-light-grey-blue))', // #F4F7FA
					'error-red': 'hsl(var(--brand-error-red))',           // #D9534F
					'success-green': 'hsl(var(--brand-success-green))',   // #28A745
					'text-primary': 'hsl(var(--brand-text-primary))',     // #FFFFFF
					'text-secondary': 'hsl(var(--brand-text-secondary))', // #333333
					'text-muted': 'hsl(var(--brand-text-muted))'          // #555555
				}
			},
			backgroundImage: {
				'gradient-hero': 'var(--gradient-hero)',
				'gradient-feature': 'var(--gradient-feature)',
				'gradient-cta': 'var(--gradient-cta)',
				'gradient-subtle': 'var(--gradient-subtle)'
			},
			boxShadow: {
				'professional': 'var(--shadow-professional)',
				'card': 'var(--shadow-card)',
				'feature': 'var(--shadow-feature)'
			},
			fontSize: {
				'medical-hero': 'var(--font-medical-hero)',
				'medical-heading': 'var(--font-medical-heading)',
				'medical-subheading': 'var(--font-medical-subheading)',
				'medical-body': 'var(--font-medical-body)'
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
